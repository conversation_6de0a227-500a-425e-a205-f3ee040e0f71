<!-- Chat Platform Navigation Bar -->
<div class="chat-platform-navbar">
    <nav class="navbar navbar-expand-lg navbar-light bg-white border-bottom shadow-sm">
        <div class="container-fluid">
            <!-- Navigation Links -->
            <div class="navbar-nav ms-auto">
                <!-- Back Button -->
                <a href="https://crm.myaiplanet.com" 
                   class="nav-link back-btn  d-flex align-items-center">
                    <i class="fa fa-arrow-left mr-2"></i>
                    <span>{{ __tr('Back') }}</span>
                </a>

                <!-- WhatsApp Chat -->
                <a href="{{ route('sso.whatsapp.contact.chat') }}" 
                   class="nav-link chat-link whatsapp-link {{ request()->routeIs('sso.whatsapp.*') ? 'active' : '' }} d-flex align-items-center">
                    <i class="fab fa-whatsapp mr-2"></i>
                    <span>{{ __tr('WhatsApp') }}</span>
                </a>

                <!-- Facebook Chat -->
                <a href="{{ route('sso.facebook.chat') }}" 
                   class="nav-link chat-link facebook-link {{ request()->routeIs('sso.facebook.*') ? 'active' : '' }} d-flex align-items-center">
                    <i class="fab fa-facebook mr-2"></i>
                    <span>{{ __tr('Facebook') }}</span>
                </a>

                <!-- Instagram Chat -->
                <a href="{{ route('sso.instagram.chat') }}" 
                   class="nav-link chat-link instagram-link {{ request()->routeIs('sso.instagram.*') ? 'active' : '' }} d-flex align-items-center">
                    <i class="fab fa-instagram mr-2"></i>
                    <span>{{ __tr('Instagram') }}</span>
                </a>
            </div>
        </div>
    </nav>
</div>

<style>
/* Chat Platform Navigation Styles */
.chat-platform-navbar {
    position: sticky !important;
    top: 0 !important;
    z-index: 1030 !important;
    border-bottom: 1px solid rgba(255,255,255,0.1) !important;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1) !important;
}

.chat-platform-navbar .navbar {
    padding: 0.75rem 1rem !important;
    background: transparent !important;
}

.chat-platform-navbar .navbar-nav {
    gap: 0.5rem !important;
}

/* Back Button Styling */
.back-btn {
    background: rgba(255, 255, 255, 0.94) !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
    border-radius: 25px !important;
    padding: 0.5rem 1rem !important;
    color: black !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
}

.back-btn:hover {
    background: rgba(255,255,255,0.2) !important;
    transform: translateX(-3px) !important;
    color: black !important;
    text-decoration: none !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2) !important;
}

.back-btn.active {
    background: rgba(255,255,255,0.3) !important;
    border-color: rgba(255,255,255,0.4) !important;
}

/* Chat Link Styling */
.chat-link {
    background: rgba(255,255,255,0.1) !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
    border-radius: 25px !important;
    padding: 0.75rem 1.25rem !important;
    color: white !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    position: relative !important;
    overflow: hidden !important;
}

.chat-link::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: -100% !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent) !important;
    transition: left 0.5s !important;
}

.chat-link:hover::before {
    left: 100% !important;
}

.chat-link:hover {
    transform: translateY(-2px) !important;
    color: white !important;
    text-decoration: none !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3) !important;
}

/* Platform-specific styling */
.whatsapp-link {
    background: white !important;
    border-color: rgba(37, 211, 102, 0.3) !important;
    color: black !important;
    box-shadow: 0 0 10px rgba(37, 211, 102, 0.4) !important;
}

.whatsapp-link:hover {
    background: linear-gradient(135deg, rgba(37, 211, 102, 1) 0%, rgba(18, 140, 126, 1) 100%) !important;
    border-color: rgba(37, 211, 102, 0.5) !important;
    color: white !important;
}

.whatsapp-link.active {
    background: linear-gradient(135deg, rgba(37, 211, 102, 1) 0%, rgba(18, 140, 126, 1) 100%) !important;
    border-color: rgba(37, 211, 102, 0.8) !important;
    box-shadow: 0 0 20px rgba(37, 211, 102, 0.4) !important;
    color: white !important;
}

.facebook-link {
background: white !important;
    border-color: rgba(24, 119, 242, 0.3) !important;
    color: black !important;
    box-shadow: 0 0 10px rgba(24, 119, 242, 0.4) !important;
}

.facebook-link:hover {
    background: linear-gradient(135deg, rgba(24, 119, 242, 1) 0%, rgba(66, 165, 245, 1) 50%, rgba(21, 101, 192, 1) 100%) !important;
    border-color: rgba(24, 119, 242, 0.5) !important;
    color: white !important;
}

.facebook-link.active {
    background: linear-gradient(135deg, rgba(24, 119, 242, 1) 0%, rgba(66, 165, 245, 1) 50%, rgba(21, 101, 192, 1) 100%) !important;
    border-color: rgba(24, 119, 242, 0.8) !important;
    box-shadow: 0 0 20px rgba(24, 119, 242, 0.4) !important;
    color: white !important;
}

.instagram-link {
    background: white !important;
    border-color: rgba(220, 39, 67, 0.3) !important;
    color: black !important;
    box-shadow: 0 0 10px rgba(220, 39, 67, 0.4) !important;
}

.instagram-link:hover {
    background: linear-gradient(45deg, rgba(240, 148, 51, 1) 0%, rgba(230, 104, 60, 1) 25%, rgba(220, 39, 67, 1) 50%, rgba(204, 35, 102, 1) 75%, rgba(188, 24, 136, 1) 100%) !important;
    border-color: rgba(220, 39, 67, 0.5) !important;
    color: white !important;
}

.instagram-link.active {
    background: linear-gradient(45deg, rgba(240, 148, 51, 1) 0%, rgba(230, 104, 60, 1) 25%, rgba(220, 39, 67, 1) 50%, rgba(204, 35, 102, 1) 75%, rgba(188, 24, 136, 1) 100%) !important;
    border-color: rgba(220, 39, 67, 0.8) !important;
    box-shadow: 0 0 20px rgba(220, 39, 67, 0.4) !important;
    color: white !important;
}

/* Icon styling */
.chat-link i {
    font-size: 1.1rem !important;
    transition: transform 0.3s ease !important;
}

.chat-link:hover i {
    transform: scale(1.1) !important;
}

.back-btn i {
    font-size: 1rem !important;
    transition: transform 0.3s ease !important;
}

.back-btn:hover i {
    transform: translateX(-2px) !important;
}

/* Text styling */
.chat-link span {
    font-weight: 600 !important;
    letter-spacing: 0.5px !important;
}

.back-btn span {
    font-weight: 500 !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-platform-navbar .navbar-nav {
        gap: 0.25rem !important;
    }
    
    .chat-link {
        padding: 0.5rem 1rem !important;
        font-size: 0.875rem !important;
    }
    
    .back-btn {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }
    
    .chat-link span {
        display: none !important;
    }
    
    .back-btn span {
        display: none !important;
    }
}

@media (max-width: 576px) {
    .chat-platform-navbar .navbar {
        padding: 0.5rem !important;
    }
    
    .chat-link {
        padding: 0.5rem !important;
        min-width: 45px !important;
        justify-content: center !important;
    }
    
    .back-btn {
        padding: 0.5rem !important;
        min-width: 45px !important;
        justify-content: center !important;
    }
}

/* Animation for active state */
@keyframes pulse-glow {
    0% { box-shadow: 0 0 20px rgba(255,255,255,0.4) !important; }
    50% { box-shadow: 0 0 30px rgba(255,255,255,0.6) !important; }
    100% { box-shadow: 0 0 20px rgba(255,255,255,0.4) !important; }
}

.chat-link.active {
    animation: pulse-glow 2s infinite !important;
}

/* Smooth transitions */
* {
    transition: all 0.3s ease !important;
}

/* Glass morphism effect */
.chat-platform-navbar::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(255,255,255,0.05) !important;
    backdrop-filter: blur(10px) !important;
    pointer-events: none !important;
}
</style>
