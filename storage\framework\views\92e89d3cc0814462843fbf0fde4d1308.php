

<?php $__env->startSection('content'); ?>
<div class="container py-4 mt-5">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2>Edit Flow</h2>
                    <a href="<?php echo e(route('whatsapp-flows.index')); ?>" class="btn btn-outline-primary">
                        <i class="fa fa-arrow-left"></i> Back to Flows
                    </a>
                </div>

                <div class="card-body">
                    <?php if(session('error')): ?>
                        <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
                    <?php endif; ?>

                    <?php if(session('success')): ?>
                        <div class="alert alert-success"><?php echo e(session('success')); ?></div>
                    <?php endif; ?>

                    <form method="POST" action="<?php echo e(route('whatsapp-flows.update', $flow['id'])); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>

                        <div class="row">
                            <!-- Flow Name -->
                            <div class="form-group col-md-6">
                                <label for="name" class="form-label">Flow Name</label>
                                <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="name" name="name" value="<?php echo e(old('name', $flow['name'])); ?>" required>
                                <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <!-- Categories - Single select dropdown -->
                            <div class="form-group col-md-6">
                                <label for="category" class="form-label">Category</label>
                                <select class="form-control <?php $__errorArgs = ['categories'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="category" name="categories[]" required>
                                    <option value="">Select a category</option>
                                    <option value="SIGN_UP" <?php echo e(in_array('SIGN_UP', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Sign Up</option>
                                    <option value="SIGN_IN" <?php echo e(in_array('SIGN_IN', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Sign In</option>
                                    <option value="APPOINTMENT_BOOKING" <?php echo e(in_array('APPOINTMENT_BOOKING', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Appointment Booking</option>
                                    <option value="LEAD_GENERATION" <?php echo e(in_array('LEAD_GENERATION', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Lead Generation</option>
                                    <option value="CONTACT_US" <?php echo e(in_array('CONTACT_US', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Contact Us</option>
                                    <option value="CUSTOMER_SUPPORT" <?php echo e(in_array('CUSTOMER_SUPPORT', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Customer Support</option>
                                    <option value="SURVEY" <?php echo e(in_array('SURVEY', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Survey</option>
                                    <option value="OTHER" <?php echo e(in_array('OTHER', old('categories', $flow['categories'])) ? 'selected' : ''); ?>>Other</option>
                                </select>
                                <small class="form-text text-muted">Select a category for your flow</small>
                                <?php $__errorArgs = ['categories'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="text-end mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> Update Flow
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp-flows/edit.blade.php ENDPATH**/ ?>