
<?php $__env->startPush('styles'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
<?php echo $__env->make('users.partials.header', [
    'title' => __tr(''),
    'description' => '',
    'class' => 'col-lg-7'
], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

<div class="container-fluid mt-lg--6">
    <div class="row mt-5">
        <!-- Header Section -->
        <div class="col-xl-12 mb-3">
            <div class="mt-5 d-flex justify-content-between align-items-center">
                <h1 class="page-title mb-0" style="color: #22A755;">
                    <i class="fas fa-sitemap me-2"></i><?php echo e(__tr(' WhatsApp Flows')); ?>

                </h1>
                <div class="d-flex">
                    <a class="lw-btn btn btn-modern btn-modern-primary btn-rounded animate__animated animate__fadeIn me-2" href="<?php echo e(route('whatsapp-flows.create')); ?>">
                        <i class="fas fa-plus-circle me-2"></i><?php echo e(__tr(' Create New Flow')); ?>

                    </a>
                    <button id="refresh-btn"
                        class="lw-btn btn btn-modern btn-rounded btn-modern-secondary me-2">
                        <i class="fas fa-sync-alt me-2"></i><?php echo e(__tr(' Refresh')); ?>

                    </button>
                    <a class="lw-btn btn btn-modern btn-rounded btn-modern-green animate__animated animate__fadeIn" target="_blank"
                        href="https://business.facebook.com/wa/manage/flows?waba_id=<?php echo e(getVendorSettings('whatsapp_business_account_id')); ?>">
                        <i class="fas fa-external-link-alt me-2"></i><?php echo e(__tr(' Manage on Meta')); ?>

                    </a>
                </div>
            </div>
        </div>

        <?php if(session('status')): ?>
            <div class="col-xl-12">
                <div class="alert alert-success" role="alert">
                    <?php echo e(session('status')); ?>

                </div>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="col-xl-12">
                <div class="alert alert-danger" role="alert">
                    <?php echo e(session('error')); ?>

                </div>
            </div>
        <?php endif; ?>

        <?php if(session('success')): ?>
            <div class="col-xl-12">
                <div class="alert alert-success" role="alert">
                    <?php echo e(session('success')); ?>

                </div>
            </div>
        <?php endif; ?>

        <!-- Modern Table Container -->
        <div class="col-xl-12">
            <div class="modern-table-container">
                <!-- Table Header with Controls -->
                <div class="table-header-controls">
                    <div class="table-title-section">
                        <h2 class="table-title"><?php echo e(__tr('WhatsApp Flows List')); ?></h2>
                    </div>
                    <div class="table-controls">
                        <div class="entries-control">
                            <label for="entries-per-page"><?php echo e(__tr('Show')); ?></label>
                            <select id="entries-per-page" class="entries-select">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100" selected>100</option>
                            </select>
                            <span class="entries-text"><?php echo e(__tr('entries per page')); ?></span>
                        </div>
                        <div class="search-control">
                            <input type="text" id="table-search" class="search-input" placeholder="<?php echo e(__tr('Search...')); ?>">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                </div>

                <!-- Loading indicator -->
                <div id="loading" class="text-center py-4 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2"><?php echo e(__tr('Fetching flows...')); ?></p>
                </div>

                <!-- Modern DataTable -->
                <div id="flows-container">
                    <?php if(empty($flows)): ?>
                        <div class="alert alert-info m-4">
                            <?php echo e(__tr('No WhatsApp flows found. Try refreshing or check your API connection.')); ?>

                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table id="flowsTable" class="table modern-datatable">
                                <thead>
                                    <tr>
                                        <th><?php echo e(__tr('ID')); ?></th>
                                        <th><?php echo e(__tr('Name')); ?></th>
                                        <th><?php echo e(__tr('Status')); ?></th>
                                        <th><?php echo e(__tr('Categories')); ?></th>
                                        <th><?php echo e(__tr('Actions')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $flows; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $flow): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="<?php echo e($flow['status'] === 'PUBLISHED' ? 'table-success' : ''); ?>" data-flow-id="<?php echo e($flow['id']); ?>">
                                            <td><?php echo e($flow['id']); ?></td>
                                            <td><?php echo e($flow['name']); ?></td>
                                            <td>
                                                <?php if($flow['status'] === 'PUBLISHED'): ?>
                                                    <div class="status-badge status-approved">
                                                        <i class="fa fa-check-circle status-icon"></i>
                                                        <span class="status-text"><?php echo e($flow['status']); ?></span>
                                                    </div>
                                                <?php elseif($flow['status'] === 'DRAFT'): ?>
                                                    <div class="status-badge status-pending">
                                                        <i class="fa fa-clock status-icon pulse"></i>
                                                        <span class="status-text"><?php echo e($flow['status']); ?></span>
                                                    </div>
                                                <?php else: ?>
                                                    <div class="status-badge status-other">
                                                        <span class="status-text"><?php echo e($flow['status']); ?></span>
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php $__currentLoopData = $flow['categories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <span class="badge badge-info mr-1"><?php echo e($category); ?></span>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </td>
                                            <td>
                                                    <?php if($flow['status'] === 'DRAFT'): ?>
                                                        <!-- Edit Button -->
                                                        <a href="<?php echo e(route('whatsapp-flows.edit', ['id' => $flow['id']])); ?>"
                                                        class="action-btn edit-btn"
                                                        title="<?php echo e(__tr('Edit')); ?>">
                                                            <i class="fa fa-edit"></i>
                                                        </a>

                                                        <!-- Delete Button -->
                                                        <form action="<?php echo e(route('whatsapp-flows.delete', ['id' => $flow['id']])); ?>"
                                                            method="POST"
                                                            style="display: inline;">
                                                            <?php echo csrf_field(); ?>
                                                            <?php echo method_field('DELETE'); ?>
                                                            <button type="submit"
                                                                    class="action-btn delete-btn delete-flow-btn"
                                                                    data-flow-id="<?php echo e($flow['id']); ?>"
                                                                    data-flow-name="<?php echo e($flow['name']); ?>"
                                                                    title="<?php echo e(__tr('Delete')); ?>">
                                                                <i class="fa fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    <?php endif; ?>

                                                    <?php if($flow['status'] !== 'DEPRECATED'): ?>
                                                        <!-- Send Button -->
                                                        <a href="<?php echo e(route('whatsapp-flows.send', ['id' => $flow['id']])); ?>"
                                                            class="action-btn campaign-btn"
                                                            title="<?php echo e(__tr('Send')); ?>">
                                                            <i class="fa fa-paper-plane"></i>
                                                        </a>
                                                    <?php endif; ?>

                                                    <!-- Preview Button -->
                                                    <a href="<?php echo e(route('whatsapp-flows.preview', ['id' => $flow['id']])); ?>"
                                                    class="action-btn preview-btn"
                                                    title="<?php echo e(__tr('Preview')); ?>">
                                                        <i class="fa fa-eye"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Table Footer with Info -->
                        <div class="table-footer">
                            <div class="table-info">
                                <span id="table-info-text"><?php echo e(__tr('Showing 0 to 0 of 0 entries')); ?></span>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Delete Flow Template -->
        <script type="text/template" id="lwDeleteFlow-template">
            <h2><?php echo e(__tr('Are You Sure!')); ?></h2>
            <p><?php echo e(__tr('You want to delete this WhatsApp Flow?')); ?></p>
        </script>
        <!-- /Delete Flow Template -->
    </div>
</div>

<style>
    /* Modern Table Container Styles */
    .modern-table-container {
        background: #ffffff !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
        overflow: hidden !important;
        margin-bottom: 2rem !important;
        border: 1px solid #e9ecef !important;
    }

    /* Table Header Controls */
    .table-header-controls {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1.5rem 2rem !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .table-title-section {
        display: flex !important;
        align-items: center !important;
    }

    .table-title {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        color: #495057 !important;
        margin: 0 !important;
    }

    .table-controls {
        display: flex !important;
        align-items: center !important;
        gap: 1.5rem !important;
    }

    .entries-control {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    .entries-control label {
        font-size: 0.875rem !important;
        color: #6c757d !important;
        margin: 0 !important;
    }

    .entries-select {
        padding: 0.375rem 0.75rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 6px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 60px !important;
    }

    .entries-text {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    .search-control {
        position: relative !important;
    }

    .search-input {
        padding: 0.5rem 1rem 0.5rem 2.5rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 8px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 200px !important;
        transition: all 0.3s ease !important;
    }

    .search-input:focus {
        outline: none !important;
        border-color: #007bff !important;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
    }

    .search-icon {
        position: absolute !important;
        left: 0.75rem !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #6c757d !important;
        font-size: 0.875rem !important;
    }

    /* Modern DataTable Styles */
    .modern-datatable {
        width: 100% !important;
        border-collapse: collapse !important;
    }

    .modern-datatable thead th {
        background: #f8f9fa !important;
        border: none !important;
        padding: 1rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        position: relative !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable thead th:hover {
        background: #e9ecef !important;
    }

    .modern-datatable tbody tr {
        border-bottom: 1px solid #f1f3f4 !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable tbody tr:hover {
        background: #f8f9fa !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .modern-datatable tbody td {
        padding: 1rem 1.5rem !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        vertical-align: middle !important;
    }

    /* Status Badge Styles */
    .status-badge {
        display: inline-flex !important;
        align-items: center !important;
        padding: 0.5rem 1rem !important;
        border-radius: 50px !important;
        font-weight: 600 !important;
        font-size: 0.75rem !important;
        letter-spacing: 0.5px !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
    }

    .status-badge:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
    }

    .status-icon {
        margin-right: 0.5rem !important;
        font-size: 0.875rem !important;
    }

    .status-approved {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        color: #155724 !important;
        border-left: 3px solid #28a745 !important;
    }

    .status-rejected {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb) !important;
        color: #721c24 !important;
        border-left: 3px solid #dc3545 !important;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        color: #856404 !important;
        border-left: 3px solid #ffc107 !important;
    }

    .status-other {
        background: linear-gradient(135deg, #e9ecef, #dee2e6) !important;
        color: #495057 !important;
        border-left: 3px solid #adb5bd !important;
    }

    /* Action Buttons Container */
    .action-buttons-container {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        justify-content: flex-start !important;
    }

    .action-btn {
        width: 36px !important;
        height: 36px !important;
        border: none !important;
        border-radius: 8px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 0.875rem !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;
        text-decoration: none !important;
    }

    .action-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        text-decoration: none !important;
    }

    .action-btn-hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }

    .campaign-btn {
        background: linear-gradient(135deg, #28a745, #20c997) !important;
        color: white !important;
    }

    .campaign-btn:hover {
        background: linear-gradient(135deg, #218838, #1ea085) !important;
        color: white !important;
    }

    .preview-btn {
        background: linear-gradient(135deg, #007bff, #0056b3) !important;
        color: white !important;
    }

    .preview-btn:hover {
        background: linear-gradient(135deg, #0056b3, #004085) !important;
        color: white !important;
    }

    .edit-btn {
        background: linear-gradient(135deg, #ffc107, #e0a800) !important;
        color: white !important;
    }

    .edit-btn:hover {
        background: linear-gradient(135deg, #e0a800, #d39e00) !important;
        color: white !important;
    }

    .delete-btn {
        background: linear-gradient(135deg, #dc3545, #c82333) !important;
        color: white !important;
    }

    .delete-btn:hover {
        background: linear-gradient(135deg, #c82333, #bd2130) !important;
        color: white !important;
    }

    /* Table Footer */
    .table-footer {
        padding: 1rem 2rem !important;
        background: #f8f9fa !important;
        border-top: 1px solid #dee2e6 !important;
    }

    .table-info {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    /* Modern Button Styles */
    .btn-modern {
        transition: all 0.3s ease !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
        font-weight: 600 !important;
        letter-spacing: 0.025em !important;
        padding: 0.5rem 1rem !important;
        margin: 0 0.2rem !important;
    }

    .btn-modern:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08) !important;
    }

    .btn-modern-primary {
        background: linear-gradient(135deg, #6e8efb, #4a6cf7) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-secondary {
        background: linear-gradient(135deg, #6edffb, #4aa3f7) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-green {
        background: linear-gradient(135deg, #0B7753, #22A755) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-green:hover {
        background: linear-gradient(135deg, #22A755, #1D9248) !important;
        color: white !important;
    }

    .btn-rounded {
        border-radius: 12px !important;
    }

    /* Pulse Animation */
    .pulse {
        animation: pulse-animation 2s infinite !important;
    }

    @keyframes pulse-animation {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* Page Title Styling */
    .page-title {
        font-size: 1.75rem !important;
        font-weight: 600 !important;
        color: #333 !important;
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1.2 !important;
        animation: fadeInLeft 0.5s ease-out !important;
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .table-header-controls {
            flex-direction: column !important;
            gap: 1rem !important;
            align-items: stretch !important;
        }

        .table-controls {
            justify-content: space-between !important;
        }
    }

    @media (max-width: 768px) {
        .table-header-controls {
            padding: 1rem !important;
        }

        .table-controls {
            flex-direction: column !important;
            gap: 1rem !important;
        }

        .entries-control {
            justify-content: center !important;
        }

        .search-control {
            width: 100% !important;
        }

        .search-input {
            width: 100% !important;
            min-width: auto !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td {
            padding: 0.75rem 1rem !important;
        }

        .action-buttons-container {
            flex-wrap: wrap !important;
            gap: 0.25rem !important;
        }

        .action-btn {
            width: 32px !important;
            height: 32px !important;
            font-size: 0.75rem !important;
        }

        .page-title {
            font-size: 1.5rem !important;
        }

        .col-xl-12 .d-flex.justify-content-between {
            flex-wrap: wrap !important;
            gap: 15px !important;
        }

        .col-xl-12 .d-flex.justify-content-between > div {
            width: 100% !important;
            justify-content: center !important;
        }
    }

    @media (max-width: 576px) {
        .table-title {
            font-size: 1.125rem !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.8rem !important;
        }

        .status-badge {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.7rem !important;
        }

        .action-btn {
            width: 28px !important;
            height: 28px !important;
            font-size: 0.7rem !important;
        }

        .btn-modern {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.875rem !important;
        }
    }

    /* Legacy badge support */
    .badge {
        font-size: 85%;
        padding: 6px 12px;
        border-radius: 6px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const refreshBtn = document.getElementById('refresh-btn');
        const loadingElement = document.getElementById('loading');
        const flowsContainer = document.getElementById('flows-container');
        let table;

        // Function to initialize custom functionality after DataTable is ready
        function initializeCustomFeatures() {
            table = $('#flowsTable').DataTable({
                "pageLength": 100,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "language": {
                    "paginate": {
                        "next": '<i class="fa fa-angle-right"></i>',
                        "previous": '<i class="fa fa-angle-left"></i>'
                    }
                },
                "dom": 'rt' // Only show table and pagination, hide other elements
            });

            if (!table) {
                // If DataTable is not ready yet, wait a bit and try again
                setTimeout(initializeCustomFeatures, 100);
                return;
            }

            // Update entries per page dropdown
            $('#entries-per-page').val(table.page.len());

            // Update table info
            updateTableInfo();

            // Add modern styling to table
            addModernTableStyling();

            // Bind custom event handlers
            bindCustomEventHandlers();
        }

        // Entries per page change handler
        $('#entries-per-page').on('change', function() {
            if (table) {
                table.page.len($(this).val()).draw();
            }
        });

        // Search functionality
        $('#table-search').on('keyup', function() {
            if (table) {
                table.search(this.value).draw();
            }
        });

        // Bind custom event handlers
        function bindCustomEventHandlers() {
            // Listen for DataTable draw events
            $('#flowsTable').on('draw.dt', function() {
                updateTableInfo();
                animateTableRows();
                addModernTableStyling();
                initializeDeleteButtons(); // Re-initialize delete buttons after table redraw
            });
        }

        // Update table info
        function updateTableInfo() {
            if (!table) return;

            const info = table.page.info();
            const start = info.start + 1;
            const end = info.end;
            const total = info.recordsTotal;
            const filtered = info.recordsDisplay;

            let infoText = `<?php echo e(__tr('Showing')); ?> ${start} <?php echo e(__tr('to')); ?> ${end} <?php echo e(__tr('of')); ?> ${filtered} <?php echo e(__tr('entries')); ?>`;
            if (filtered !== total) {
                infoText += ` (<?php echo e(__tr('filtered from')); ?> ${total} <?php echo e(__tr('total entries')); ?>)`;
            }

            $('#table-info-text').text(infoText);
        }

        // Animate table rows
        function animateTableRows() {
            const rows = document.querySelectorAll('#flowsTable tbody tr');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.classList.add('animate__animated', 'animate__fadeInUp');
                    row.style.opacity = '1';
                }, index * 50);
            });
        }

        // Initialize delete buttons
        function initializeDeleteButtons() {
            document.querySelectorAll('.delete-flow-btn').forEach(button => {
                // Remove existing event listeners to prevent duplicates
                button.removeEventListener('click', handleDeleteClick);
                button.addEventListener('click', handleDeleteClick);
            });
        }

        // Handle delete button click
        function handleDeleteClick(e) {
            e.preventDefault();

            // Get the form element
            const form = this.closest('form');

            // Get flow details from data attributes
            const flowId = this.getAttribute('data-flow-id');
            const flowName = this.getAttribute('data-flow-name');

            // Show confirmation
            if (confirm('<?php echo e(__tr("Are you sure you want to delete flow")); ?> "' + flowName + '"? <?php echo e(__tr("This action cannot be undone.")); ?>')) {
                // Submit the form directly
                form.submit();
            }
        }

        // Refresh Functionality
        if (refreshBtn) {
            refreshBtn.addEventListener('click', function() {
                loadingElement.classList.remove('d-none');
                flowsContainer.classList.add('d-none');
                window.location.reload();
            });
        }

        // Initialize animations for main buttons
        const buttons = document.querySelectorAll('.btn-modern');
        buttons.forEach((button, index) => {
            setTimeout(() => {
                button.classList.add('animate__animated', 'animate__fadeIn');
                button.style.opacity = '1';
            }, index * 150);
        });

        // Add hover effects to modern buttons
        $(document).on('mouseenter', '.btn-modern', function() {
            $(this).addClass('animate__animated animate__pulse');
        }).on('mouseleave', '.btn-modern', function() {
            $(this).removeClass('animate__animated animate__pulse');
        });

        // Status badge animations
        $('#flowsTable').on('draw.dt', function() {
            const statusBadges = document.querySelectorAll('.status-badge');
            statusBadges.forEach((badge, index) => {
                badge.classList.remove('animate__fadeIn');
                void badge.offsetWidth;

                setTimeout(() => {
                    badge.classList.add('animate__fadeIn');

                    badge.addEventListener('mouseenter', function() {
                        this.classList.add('animate__pulse');
                    });

                    badge.addEventListener('mouseleave', function() {
                        this.classList.remove('animate__pulse');
                    });

                    if (badge.classList.contains('status-approved')) {
                        const icon = badge.querySelector('.status-icon');
                        icon.classList.add('animate__animated', 'animate__bounceIn');
                        setTimeout(() => {
                            icon.classList.remove('animate__bounceIn');
                        }, 1000);
                    }
                }, index * 150);
            });
        });

        // Action button hover effects
        $(document).on('mouseenter', '.action-btn', function() {
            $(this).addClass('action-btn-hover');
        }).on('mouseleave', '.action-btn', function() {
            $(this).removeClass('action-btn-hover');
        });

        // Add modern table styling function
        function addModernTableStyling() {
            // Add hover effects to table rows
            $('#flowsTable tbody tr').hover(
                function() {
                    $(this).addClass('table-row-hover');
                },
                function() {
                    $(this).removeClass('table-row-hover');
                }
            );

            // Add click effects to table headers
            $('#flowsTable thead th').click(function() {
                $('#flowsTable thead th').removeClass('sort-active');
                $(this).addClass('sort-active');
            });
        }

        // Add modern table row hover class
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .table-row-hover {
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                }
                .sort-active {
                    background: linear-gradient(135deg, #007bff, #0056b3) !important;
                    color: white !important;
                }
                .sort-active .sort-icon {
                    color: white !important;
                }
            `)
            .appendTo('head');

        // Start initialization after a short delay to ensure DataTable is ready
        setTimeout(initializeCustomFeatures, 500);

        // Initialize delete buttons on page load
        initializeDeleteButtons();
    });
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', ['title' => __tr('WhatsApp Flows')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/whatsapp-flows/index.blade.php ENDPATH**/ ?>