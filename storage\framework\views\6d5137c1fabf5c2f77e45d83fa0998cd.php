
<?php
$hasActiveLicense = true;
if(isLoggedIn() and (request()->route()->getName() != 'manage.configuration.product_registration') and (!getAppSettings('product_registration', 'registration_id') or sha1(array_get($_SERVER, 'HTTP_HOST', '') . getAppSettings('product_registration', 'registration_id') . '4.5+') !== getAppSettings('product_registration', 'signature'))) {
    $hasActiveLicense = false;
    if(hasCentralAccess()) {
        header("Location: " . route('manage.configuration.product_registration'));
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e($CURRENT_LOCALE_DIRECTION); ?>">
<head>
    <!-- SweetAlert2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
<!-- SweetAlert2 JS -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title> <?php echo e((isset($title) and $title) ? $title : __tr('Welcome')); ?> - <?php echo e(getAppSettings('name')); ?></title>
    <!-- Favicon -->
    <link href="<?php echo e(getAppSettings('favicon_image_url')); ?>" rel="icon">
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Nunito+Sans:ital,opsz,wght@0,6..12,200..1000;1,6..12,200..1000&family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap" rel="stylesheet">
    <?php echo $__env->yieldPushContent('head'); ?>
    <?php echo __yesset([
        'static-assets/packages/fontawesome/css/all.css',
        'dist/css/common-vendorlibs.css',
        'dist/css/vendorlibs.css',
        'argon/css/argon.min.css',
        'dist/css/app.css',
    ]); ?>

    
    
    <link href="<?php echo e(route('app.load_custom_style')); ?>" rel="stylesheet" />
    <!-- ...other meta and CSS... -->
    <?php echo $__env->yieldPushContent('styles'); ?>
    <style>
        .card {
            background-color: #ffffff;
        }
        .circular-icon {
            background-color: #E2E5E9;
            width: 30px;
            height: 30px; 
            border-radius: 50%; 
            display: inline-flex; 
            align-items: center; 
            justify-content: center; 
            font-size: 15px !important;
        }
        #navbar-main {
            background-color: #ffffff !important;
        }
        .dtr-control::before {
            background-color: #0081fb !important;
        }
        .switchery {
            height: 20px !important;
            width: 40px;
        }
        .switchery > small {
            border-radius: 10px;
            height: 20px !important;
            width: 20px;
        }
        a.lw-btn {
            color: #ffffff !important;
        }
        
        .btn-sm.btn-default {
            background-color:rgb(70, 180, 166) !important; /* Purple */
            color: white;
        }
        .btn-danger.btn-sm {
            
        }
        .btn-warning.btn-sm {
            background: linear-gradient(135deg, #fcbd00, #ffe207) !important;
        }
        .btn-light.btn-sm {
            background: rgb(44, 119, 46) !important;
        }
        .btn-light.btn-sm:hover{
            background: rgb(27, 73, 28) !important;

        } 
        .btn-dark.btn-sm {
            background-color:rgb(69, 56, 173)  !important; /* Dark Gray */
            color: white;
        }
        .btn-dark.btn-sm:hover {
            background-color:rgb(60, 44, 121)  !important;
        }
        th {
            background-color:rgb(11, 119, 83) !important;
            color: #ffffff !important;
        }
        td {
            background-color: #F4F6F9;
            color: #333333;
        }
        td a {
            color: #0066C8 !important;
        }
       
        .empty {
            
        }
        legend {
            background-color: #EBF5FF !important;
            font-weight: 500;
        }

        /* Hide sidebar and navbar */
        .hide-navigation #sidenav-main {
            display: none !important;
        }
        .hide-navigation .main-content {
            margin-left: 0 !important;
        }
        .hide-navigation #navbar-main {
            display: none !important;
        }
        .hide-navigation .main-content {
            padding-top: 0 !important;
        }
 
    </style>
</head>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Function to check if current route should hide navigation
        function checkRouteForNavigation() {
            const currentPath = window.location.pathname;
            const currentUrl = window.location.href;
            
            // Routes that should hide navigation (Chat routes)
            const hideNavigationRoutes = [
                '/whatsapp/contact/chat',
                'whatsapp/contact/chat',
                'sso.whatsapp.contact.chat',
                '/facebook/chat',
                'facebook/chat',
                'sso.facebook.chat',
                '/instagram/chat',
                'instagram/chat',
                'sso.instagram.chat'
            ];
            
            // Check if current route matches any of the hide navigation routes
            const shouldHideNavigation = hideNavigationRoutes.some(route => 
                currentPath.includes(route) || currentUrl.includes(route)
            );
            
            // Apply or remove hide-navigation class
            if (shouldHideNavigation) {
                document.body.classList.add('hide-navigation');
            } else {
                document.body.classList.remove('hide-navigation');
            }
        }
        
        // Check route on page load
        checkRouteForNavigation();
        
        // Check route when navigating (for SPA-like behavior)
        window.addEventListener('popstate', checkRouteForNavigation);
        
        // Monitor for route changes in case of AJAX navigation
        let currentUrl = window.location.href;
        setInterval(() => {
            if (currentUrl !== window.location.href) {
                currentUrl = window.location.href;
                checkRouteForNavigation();
            }
        }, 100);
        
        document.querySelectorAll('.redirect-to-setup').forEach(link => {
            link.addEventListener('click', function (e) {
                e.preventDefault();
                Swal.fire({
                    icon: 'info',
                    title: 'WhatsApp Setup Required',
                    text: 'Please setup WhatsApp to access this feature',
                    confirmButtonText: 'Go to Setup'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = "<?php echo e(route('vendor.settings.read', ['pageType' => 'whatsapp-cloud-api-setup'])); ?>";
                    }
                });
            });
        });
    });
</script>

<body class="<?php if(hasVendorAccess() or hasVendorUserAccess()): ?> empty <?php endif; ?> pb-5 <?php if(isLoggedIn()): ?> lw-authenticated-page <?php else: ?> lw-guest-page <?php endif; ?> <?php echo e($class ?? ''); ?>" x-cloak x-data="{disableSoundForMessageNotification:<?php echo e(getVendorSettings('is_disabled_message_sound_notification') ? 1 : 0); ?>,unreadMessagesCount:null}">
    <?php if(auth()->guard()->check()): ?>
    <?php echo $__env->make('layouts.navbars.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <div class="main-content">
        <?php echo $__env->make('layouts.navbars.navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php if(isDemo()): ?>
        <div class="container">
            <div class="row">
                <a class="alert alert-danger col-12 mt-md-8 mt-sm-4 mb-sm--3 text-center text-white" target="_blank" href="https://codecanyon.net/item/whatsjet-saas-a-whatsapp-marketing-platform-with-bulk-sending-campaigns-chat-bots/51167362">
                    <?php echo e(__tr('Please Note: We sell this script only through CodeCanyon.net at ')); ?> https://codecanyon.net/item/whatsjet-saas-a-whatsapp-marketing-platform-with-bulk-sending-campaigns-chat-bots/51167362
                </a>
            </div>
        </div>
        <?php endif; ?>
            <?php if($hasActiveLicense): ?>
            <?php if(hasVendorAccess()): ?>
            <div class="container">
                <div class="row">
                    <div class="col-12 mt-5 mb--7 pt-5 text-center">
                        <?php
                        $vendorPlanDetails = vendorPlanDetails(null, null, getVendorId());
                        ?>
                        <?php if(!$vendorPlanDetails->hasActivePlan()): ?>
                            <div class="alert alert-danger">
                                <?php echo e($vendorPlanDetails->message); ?>

                            </div>
                        <?php elseif($vendorPlanDetails->is_expiring): ?>
                            <div class="alert alert-warning">
                                <?php echo e(__tr('Your subscription plan is expiring on __endAt__', [
                                    '__endAt__' => formatDate($vendorPlanDetails->ends_at)
                                ])); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
            <?php echo $__env->yieldContent('content'); ?>
            <?php else: ?>
            <div class="container">
                <div class="row">
                    <div class="col-12 my-5 py-5 text-center">
                       <div class="card my-5 p-5">
                        <i class="fas fa-exclamation-triangle fa-6x mb-4 text-warning"></i>
                        <div class="alert alert-danger my-5">
                            <?php echo e(__tr('Product has not been verified yet, please contact via profile or product page.')); ?>

                        </div>
                       </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
    </div>
    <?php if(auth()->guard()->guest()): ?>
    <?php echo $__env->make('layouts.footers.guest', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>
    <?= __yesset(['dist/js/common-vendorlibs.js','dist/js/vendorlibs.js', 'argon/bootstrap/dist/js/bootstrap.bundle.min.js', 'argon/js/argon.js'], true) ?>
    <?php echo $__env->yieldPushContent('js'); ?>
    <?php if(hasVendorAccess() or hasVendorUserAccess()): ?>
    
    <?php if (isset($component)) { $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.modal','data' => ['id' => 'lwScanMeDialog','header' => __tr('Scan QR Code to Start Chat')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwScanMeDialog','header' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__tr('Scan QR Code to Start Chat'))]); ?>
        <?php if(getVendorSettings('current_phone_number_number')): ?>
        <div class="alert alert-dark text-center text-success">
            <?php echo e(__tr('You can use following QR Codes to invite people to get connect with you on this platform.')); ?>

        </div>
        <?php if(!empty(getVendorSettings('whatsapp_phone_numbers'))): ?>
        <?php $__currentLoopData = getVendorSettings('whatsapp_phone_numbers'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $whatsappPhoneNumber): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <fieldset class="text-center">
            <legend class="text-center"><?php echo e($whatsappPhoneNumber['verified_name']); ?> (<?php echo e($whatsappPhoneNumber['display_phone_number']); ?>)</legend>
        <div class="text-center">
            <img class="lw-qr-image" src="<?php echo e(route('vendor.whatsapp_qr', [
            'vendorUid' => getVendorUid(),
            'phoneNumber' => cleanDisplayPhoneNumber($whatsappPhoneNumber['display_phone_number']),
        ])); ?>">
        </div>
        <div class="form-group">
            <h3 class="text-muted"><?php echo e(__tr('Phone Number')); ?></h3>
            <h3 class="text-success"><?php echo e($whatsappPhoneNumber['display_phone_number']); ?></h3>
            <label for="lwWhatsAppQRImage<?php echo e($loop->index); ?>"><?php echo e(__tr('URL for QR Image')); ?></label>
            <div class="input-group">
                <input type="text" class="form-control" readonly id="lwWhatsAppQRImage<?php echo e($loop->index); ?>" value="<?php echo e(route('vendor.whatsapp_qr', [
                    'vendorUid' => getVendorUid(),
                    'phoneNumber' => cleanDisplayPhoneNumber($whatsappPhoneNumber['display_phone_number']),
                ])); ?>">
                <div class="input-group-append">
                    <button class="btn btn-outline-light" type="button"
                        onclick="lwCopyToClipboard('lwWhatsAppQRImage<?php echo e($loop->index); ?>')">
                        <?= __tr('Copy') ?>
                    </button>
                </div>
            </div>
        </div>
        <div class="form-group">
            <h3 class="text-muted"><?php echo e(__tr('WhatsApp URL')); ?></h3>
            <div class="input-group">
                <input type="text" class="form-control" readonly id="lwWhatsAppUrl<?php echo e($loop->index); ?>" value="https://wa.me/<?php echo e(cleanDisplayPhoneNumber($whatsappPhoneNumber['display_phone_number'])); ?>">
                <div class="input-group-append">
                    <button class="btn btn-outline-light" type="button"
                        onclick="lwCopyToClipboard('lwWhatsAppUrl<?php echo e($loop->index); ?>')">
                        <?= __tr('Copy') ?>
                    </button>
                    <a type="button" class="btn btn-outline-success" target="_blank" href="https://api.whatsapp.com/send?phone=<?php echo e(cleanDisplayPhoneNumber($whatsappPhoneNumber['display_phone_number'])); ?>"><i class="fab fa-whatsapp"></i>  <?php echo e(__tr('WhatsApp Now')); ?></a>
                </div>
            </div>
        </div>
        </fieldset>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php else: ?>
        <div class="alert alert-info"><?php echo e(__tr('Please resync phone numbers.')); ?></div>
        <?php endif; ?>
        <?php else: ?>
        <div class="text-danger">
            <?php echo e(__tr('Phone number does not configured yet.')); ?>

        </div>
        <?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $attributes = $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $component = $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
    
    <template x-if="!disableSoundForMessageNotification">
        <audio id="lwMessageAlertTone">
            <source src="<?= asset('/static-assets/audio/whatsapp-notification-tone.mp3'); ?>" type="audio/mpeg">
        </audio>
     </template>
    <?php endif; ?>
    <script>
        (function($) {
            'use strict';
            window.appConfig = {
                debug: "<?php echo e(config('app.debug')); ?>",
                csrf_token: "<?php echo e(csrf_token()); ?>",
                locale : '<?php echo e(app()->getLocale()); ?>',
                vendorUid : '<?php echo e(getVendorUid()); ?>',
                broadcast_connection_driver: "<?php echo e(getAppSettings('broadcast_connection_driver')); ?>",
                pusher : {
                    key : "<?php echo e(config('broadcasting.connections.pusher.key')); ?>",
                    cluster : "<?php echo e(config('broadcasting.connections.pusher.options.cluster')); ?>",
                    host : "<?php echo e(config('broadcasting.connections.pusher.options.host')); ?>",
                    port : "<?php echo e(config('broadcasting.connections.pusher.options.port')); ?>",
                    useTLS : "<?php echo e(config('broadcasting.connections.pusher.options.useTLS')); ?>",
                    encrypted : "<?php echo e(config('broadcasting.connections.pusher.options.encrypted')); ?>",
                    authEndpoint : "<?php echo e(url('/broadcasting/auth')); ?>"
                },
            }
        })(jQuery);
    </script>
    <?= __yesset(
        [
            'dist/js/jsware.js',
            'dist/js/app.js',
            // keep it last
            'dist/js/alpinejs.min.js',
        ],
        true,
    ) ?>
    <?php if(hasVendorAccess() or hasVendorUserAccess()): ?>
    
    <?php echo __yesset('dist/js/bootstrap.js', true); ?>

    <?php endif; ?>
    <?php echo $__env->yieldPushContent('vendorLibs'); ?>
    <script src="<?php echo e(route('vendor.load_server_compiled_js')); ?>"></script>
    <?php echo $__env->yieldPushContent('footer'); ?>
    <?php echo $__env->yieldPushContent('appScripts'); ?>
    <script>
    (function($) {
        'use strict';
        <?php if(session('alertMessage')): ?>
            showAlert("<?php echo e(session('alertMessage')); ?>", "<?php echo e(session('alertMessageType') ?? 'info'); ?>");
            <?php
                session('alertMessage', null);
                session('alertMessageType', null);
            ?>
        <?php endif; ?>
        <?php
        $isRestrictedVendorUser = (!hasVendorAccess() ? hasVendorAccess('assigned_chats_only') : false);
        ?>
        var isRestrictedVendorUser = <?php echo e($isRestrictedVendorUser ? 1 : 0); ?>,
            loggedInUserId = '<?php echo e(getUserId()); ?>';
        __Utils.setTranslation({
            'processing': "<?php echo e(__tr('processing')); ?>",
            'uploader_default_text': "<span class='filepond--label-action'><?php echo e(__tr('Drag & Drop Files or Browse')); ?></span>",
            "confirmation_yes": "<?php echo e(__tr('Yes')); ?>",
            "confirmation_no": "<?php echo e(__tr('No')); ?>"
        });

        <?php if(hasVendorAccess() or hasVendorUserAccess()): ?>
            var broadcastActionDebounce,
                campaignActionDebounce,
                lastEventData,
                lastCampaignStatus;
            window.Echo.private(`vendor-channel.${window.appConfig.vendorUid}`).listen('.VendorChannelBroadcast', function (data) {
                // if the event data matched does not need to process it
                if(_.isEqual(lastEventData, data)) {
                    return true;
                }
                if(!data.campaignUid && (!isRestrictedVendorUser || (isRestrictedVendorUser && (data.assignedUserId == loggedInUserId)))) {
                    // chat updates
                    if(data.contactUid && $('[data-contact-uid=' + data.contactUid + ']').length) {
                        __DataRequest.get(__Utils.apiURL("<?php echo e(route('vendor.chat_message.data.read', ['contactUid', 'way'])); ?><?php echo e(((isset($assigned) and $assigned) ? '?assigned=to-me' : '')); ?>", {'contactUid': data.contactUid, 'way':'prepend'}),{}, function(responseData) {
                            __DataRequest.updateModels({
                                '@whatsappMessageLogs' : 'append',
                                'whatsappMessageLogs':responseData.client_models.whatsappMessageLogs
                            });
                            window.lwScrollTo('#lwEndOfChats', true);
                        });
                    } else if((!isRestrictedVendorUser || (isRestrictedVendorUser && (data.assignedUserId == loggedInUserId)))) {
                        // play the sound for incoming message notifications
                        if(data.isNewIncomingMessage && $('#lwMessageAlertTone').length) {
                            $('#lwMessageAlertTone')[0].play();
                        };
                    };
                }
                lastEventData = _.cloneDeep(data);
                clearTimeout(broadcastActionDebounce);
                broadcastActionDebounce = setTimeout(function() {
                    // generic model updates
                    if(data.eventModelUpdate) {
                        __DataRequest.updateModels(data.eventModelUpdate);
                    }
                    <?php if(hasVendorAccess('messaging')): ?>
                    if(!data.campaignUid && (!isRestrictedVendorUser || (isRestrictedVendorUser && (data.assignedUserId == loggedInUserId)))) {
                        // is incoming message
                        if(data.isNewIncomingMessage) {
                            __DataRequest.get("<?php echo e(route('vendor.chat_message.read.unread_count')); ?>",{}, function(responseData) {});
                        };
                        // contact list update
                        if($('.lw-whatsapp-chat-window').length) {
                            __DataRequest.get(__Utils.apiURL("<?php echo route('vendor.contacts.data.read', ['contactUid','way' => 'append','request_contact' => '', 'assigned'=> ($assigned ?? '')]); ?>", {'contactUid': $('#lwWhatsAppChatWindow').data('contact-uid'),'request_contact' : 'request_contact=' + data.contactUid + '&'}),{}, function() {});
                        }
                    }
                    <?php endif; ?>
                }, 1000);
                <?php if(hasVendorAccess('messaging')): ?>
                // 10 seconds for campaign
                    clearTimeout(campaignActionDebounce);
                    campaignActionDebounce = setTimeout(function() {
                        // campaign data update
                        if(data.campaignUid && $('.lw-campaign-window-' + data.campaignUid).length) {
                            __DataRequest.get(__Utils.apiURL("<?php echo e(route('vendor.campaign.status.data', ['campaignUid'])); ?>", {'campaignUid': data.campaignUid}),{}, function(responseData) {
                                if(responseData.data.campaignStatus != lastCampaignStatus) {
                                    window.reloadDT('#lwCampaignQueueLog');
                                }
                                lastCampaignStatus = responseData.data.campaignStatus;
                            });
                        };
                    }, 10000);
                <?php endif; ?>
            });
        <?php if(hasVendorAccess('messaging')): ?>
        // initially get the unread count on page loads
        __DataRequest.get("<?php echo e(route('vendor.chat_message.read.unread_count')); ?>",{}, function() {});
        <?php endif; ?>
    <?php endif; ?>
    })(jQuery);
    </script>
    <?php echo getAppSettings('page_footer_code_all'); ?>

    <?php if(isLoggedIn()): ?>
    <?php echo getAppSettings('page_footer_code_logged_user_only'); ?>

    <?php endif; ?>
    <?php $__env->startPush('scripts'); ?>
<script>
    function alertAndRedirect(event, redirectUrl) {
    event.preventDefault();

    Swal.fire({
        title: 'WhatsApp Setup Required',
        text: 'Please complete your WhatsApp API setup to access this feature.',
        icon: 'warning',
        confirmButtonText: 'Go to Setup',
        confirmButtonColor: '#3085d6',
        background: '#f7f7f7',
        customClass: {
            popup: 'rounded-lg shadow-lg',
            title: 'text-lg font-semibold',
            confirmButton: 'btn btn-primary'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = redirectUrl;
        }
    });
}

</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/layouts/app.blade.php ENDPATH**/ ?>