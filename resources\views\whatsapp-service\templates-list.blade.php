@php
/**
* Component : WhatsAppService
* Controller : WhatsAppServiceController
* File : templates.list.blade.php
----------------------------------------------------------------------------- */
@endphp
@extends('layouts.app', ['title' => __tr('Templates List')])
@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
@endpush
@section('content')
@include('users.partials.header', [
'description' => '',
'class' => 'col-lg-7'
])
<div class="container-fluid mt-lg--6">
    <div class="row mt-3">
        <!-- Header Section -->
        <div class="col-xl-12 mb-3">
            <div class="mt-5 d-flex justify-content-between align-items-center">
                <h1 class="page-title mb-0" style="color: #22A755;">
                    <i class="fas fa-file-alt me-2"></i>{{ __tr(' WhatsApp Templates') }}
                </h1>
                <div class="d-flex">
                    <a class="lw-btn btn btn-modern btn-modern-primary btn-rounded animate__animated animate__fadeIn me-2" href="{{ route('vendor.whatsapp_service.templates.read.new_view') }}">
                        <i class="fas fa-plus-circle me-2"></i>{{ __tr(' Create Template') }}
                    </a>
                    <a class="lw-btn btn btn-modern btn-rounded btn-modern-secondary sync-templates-btn lw-ajax-link-action me-2"
                        data-callback="reloadDtOnSuccess" data-method="post"
                        href="{{ route('vendor.whatsapp_service.templates.write.sync') }}">
                        <i class="fas fa-sync-alt me-2"></i>{{ __tr(' Sync') }}
                    </a>
                    <a class="lw-btn btn btn-modern btn-rounded btn-modern-green animate__animated animate__fadeIn" target="_blank"
                        href="https://business.facebook.com/wa/manage/message-templates/?waba_id={{ getVendorSettings('whatsapp_business_account_id') }}">
                        <i class="fas fa-external-link-alt me-2"></i>{{ __tr(' Manage on Meta') }}
                    </a>
                </div>
            </div>
        </div>

        <!-- Modern Table Container -->
        <div class="col-xl-12">
            <div class="modern-table-container">
                <!-- Table Header with Controls -->
                <div class="table-header-controls">
                    <div class="table-title-section">
                        <h2 class="table-title">{{ __tr('Templates List') }}</h2>
                    </div>
                    <div class="table-controls">
                        <div class="entries-control">
                            <label for="entries-per-page">{{ __tr('Show') }}</label>
                            <select id="entries-per-page" class="entries-select">
                                <option value="10">10</option>
                                <option value="25">25</option>
                                <option value="50">50</option>
                                <option value="100" selected>100</option>
                            </select>
                            <span class="entries-text">{{ __tr('entries per page') }}</span>
                        </div>
                        <div class="search-control">
                            <input type="text" id="table-search" class="search-input" placeholder="{{ __tr('Search...') }}">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                </div>

                <!-- Modern DataTable -->
                <div class="table-responsive">
                    <x-lw.datatable id="lwTemplatesList" data-page-length="100" :url="route('vendor.whatsapp_service.templates.read.list')" class="modern-datatable">
                        <th data-orderable="true" data-name="template_name">{{ __tr('Name') }}</th>
                        <th data-orderable="true" data-name="language">{{ __tr('Language') }}</th>
                        <th data-orderable="true" data-name="category">{{ __tr('Category') }}</th>
                        <th data-template="#templatesStatusColumnTemplate" name="null" class="status-column">{{ __tr('Status') }}</th>
                        <th data-orderable="true" data-order-by="true" data-order-type="desc" data-name="updated_at">{{ __tr('Updated On') }}</th>
                        <th data-template="#templatesActionColumnTemplate" name="null">{{ __tr('Action') }}</th>
                    </x-lw.datatable>
                </div>

                <!-- Table Footer with Info -->
                <div class="table-footer">
                    <div class="table-info">
                        <span id="table-info-text">{{ __tr('Showing 0 to 0 of 0 entries') }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal for Template Preview -->
        <x-lw.modal id="lwTemplatePreview" :header="__tr('Template Preview')" :modalSize="'modal-sm'" :hasForm="true" x-data="{selectedTemplate:''}">
            <div id="lwTemplatePreviewForm">
                <div id="lwTemplateStructureContainer" class="lw-form-modal-body"></div>
                <div class="modal-footer" id="lwTemplateStructureContainerActions"></div>
                <script type="text/template" id="lwTemplateStructureContainerActions-template">
                    <% if(__tData.template_status == 'APPROVED') { %>
                    <a title="{{  __tr('Create Campaign using this template') }}" class="lw-btn btn btn-primary"  href="{{ route('vendor.campaign.new.view') }}?use_template=<%- __tData._uid %>"><i class="fas fa-plus-circle me-2"></i> {{  __tr('Create Campaign') }}</a>
                    <% } %>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __tr('Close') }}</button>
                </script>
            </div>
        </x-lw.modal>

        <!-- Status Column Template -->
        <script type="text/template" id="templatesStatusColumnTemplate">
            <% if(__tData.status == 'APPROVED') { %>
                <div class="status-badge status-approved">
                    <i class="fa fa-check-circle status-icon"></i>
                    <span class="status-text">{{  __tr('APPROVED') }}</span>
                </div>
            <% } else if(__tData.status == 'REJECTED') { %>
                <div class="status-badge status-rejected">
                    <i class="fa fa-times-circle status-icon"></i>
                    <span class="status-text">{{  __tr('REJECTED') }}</span>
                </div>
            <% } else if(__tData.status == 'PENDING') { %>
                <div class="status-badge status-pending">
                    <i class="fa fa-clock status-icon pulse"></i>
                    <span class="status-text">{{  __tr('PENDING') }}</span>
                </div>
            <% } else { %>
                <div class="status-badge status-other">
                    <span class="status-text"><%- __tData.status %></span>
                </div>
            <% } %>
        </script>

        <!-- Action Column Template -->
        <script type="text/template" id="templatesActionColumnTemplate">
            <div class="action-buttons-container">
                <% if(__tData.status == 'APPROVED') { %>
                <!-- Create Campaign Button -->
                <a title="{{ __tr('Create Campaign using this template') }}"
                class="action-btn campaign-btn"
                href="{{ route('vendor.campaign.new.view') }}?use_template=<%- __tData._uid %>">
                    <i class="fas fa-plus-circle"></i>
                </a>
                <% } %>

                <!-- Preview Template -->
                <a data-pre-callback="appFuncs.clearContainer"
                    title="{{ __tr('Template Preview') }}"
                    class="action-btn preview-btn lw-ajax-link-action"
                    data-method="post"
                    data-post-data="<%- toJsonString({'template_selection': __tData._uid,'template_status': __tData.status}) %>"
                    data-response-template="#lwTemplateStructureContainerActions"
                    href="{{ route('vendor.request.template.view') }}?only-preview=1"
                    data-toggle="modal"
                    data-target="#lwTemplatePreview">
                        <i class="fa fa-eye"></i>
                </a>

                <!-- Edit Template -->
                <a title="{{ __tr('Edit Template') }}"
                class="action-btn edit-btn"
                href="<%= __Utils.apiURL('{{ route('vendor.whatsapp_service.templates.read.update_view',['whatsappTemplateUid']) }}', { 'whatsappTemplateUid': __tData._uid }) %>">
                    <i class="fa fa-edit"></i>
                </a>

                <!-- Delete Template -->
                <a title="{{ __tr('Delete Template') }}"
                    data-callback="reloadDtOnSuccess"
                    data-method="post"
                    data-confirm="#lwConfirmTemplateDelete"
                    data-confirm-params="<%- toJsonString({'templateName': __tData.template_name}) %>"
                    class="action-btn delete-btn lw-ajax-link-action"
                    href="<%= __Utils.apiURL('{{ route('vendor.whatsapp_service.templates.write.delete',['whatsappTemplateUid']) }}', { 'whatsappTemplateUid': __tData._uid }) %>">
                        <i class="fa fa-trash"></i>
                </a>
            </div>
        </script>

        <!-- Delete Confirmation Template -->
        <script type="text/template" id="lwConfirmTemplateDelete">
            <h3>{!! __tr('Are you sure you want to delete __templateName__ template', [
                '__templateName__' => '<strong><%- __tData.templateName %></strong>'
                ]) !!}</h3>
        </script>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Wait for the existing DataTable to be initialized
        let table;
        
        // Function to initialize custom functionality after DataTable is ready
        function initializeCustomFeatures() {
            table = $('#lwTemplatesList').DataTable();
            
            if (!table) {
                // If DataTable is not ready yet, wait a bit and try again
                setTimeout(initializeCustomFeatures, 100);
                return;
            }
            
            // Update entries per page dropdown
            $('#entries-per-page').val(table.page.len());
            
            // Update table info
            updateTableInfo();
            
            // Add modern styling to table
            addModernTableStyling();
            
            // Bind custom event handlers
            bindCustomEventHandlers();
        }

        // Entries per page change handler
        $('#entries-per-page').on('change', function() {
            if (table) {
                table.page.len($(this).val()).draw();
            }
        });

        // Search functionality
        $('#table-search').on('keyup', function() {
            if (table) {
                table.search(this.value).draw();
            }
        });

        // Bind custom event handlers
        function bindCustomEventHandlers() {
            // Listen for DataTable draw events
            $('#lwTemplatesList').on('draw.dt', function() {
                updateTableInfo();
                animateTableRows();
                addModernTableStyling();
            });
        }

        // Update table info
        function updateTableInfo() {
            if (!table) return;
            
            const info = table.page.info();
            const start = info.start + 1;
            const end = info.end;
            const total = info.recordsTotal;
            const filtered = info.recordsDisplay;
            
            let infoText = `{{ __tr('Showing') }} ${start} {{ __tr('to') }} ${end} {{ __tr('of') }} ${filtered} {{ __tr('entries') }}`;
            if (filtered !== total) {
                infoText += ` ({{ __tr('filtered from') }} ${total} {{ __tr('total entries') }})`;
            }
            
            $('#table-info-text').text(infoText);
        }

        // Animate table rows
        function animateTableRows() {
            const rows = document.querySelectorAll('#lwTemplatesList tbody tr');
            rows.forEach((row, index) => {
                setTimeout(() => {
                    row.classList.add('animate__animated', 'animate__fadeInUp');
                    row.style.opacity = '1';
                }, index * 50);
            });
        }

        // Initialize animations for main buttons
        const buttons = document.querySelectorAll('.btn-modern');
        buttons.forEach((button, index) => {
            setTimeout(() => {
                button.classList.add('animate__animated', 'animate__fadeIn');
                button.style.opacity = '1';
            }, index * 150);
        });

        // Add hover effects to modern buttons
        $(document).on('mouseenter', '.btn-modern', function() {
            $(this).addClass('animate__animated animate__pulse');
        }).on('mouseleave', '.btn-modern', function() {
            $(this).removeClass('animate__animated animate__pulse');
        });

        // Status badge animations
        $('#lwTemplatesList').on('draw.dt', function() {
            const statusBadges = document.querySelectorAll('.status-badge');
            statusBadges.forEach((badge, index) => {
                badge.classList.remove('animate__fadeIn');
                void badge.offsetWidth;
                
                setTimeout(() => {
                    badge.classList.add('animate__fadeIn');
                    
                    badge.addEventListener('mouseenter', function() {
                        this.classList.add('animate__pulse');
                    });
                    
                    badge.addEventListener('mouseleave', function() {
                        this.classList.remove('animate__pulse');
                    });
                    
                    if (badge.classList.contains('status-approved')) {
                        const icon = badge.querySelector('.status-icon');
                        icon.classList.add('animate__animated', 'animate__bounceIn');
                        setTimeout(() => {
                            icon.classList.remove('animate__bounceIn');
                        }, 1000);
                    }
                }, index * 150);
            });
        });

        // Action button hover effects
        $(document).on('mouseenter', '.action-btn', function() {
            $(this).addClass('action-btn-hover');
        }).on('mouseleave', '.action-btn', function() {
            $(this).removeClass('action-btn-hover');
        });

        // Add modern table styling function
        function addModernTableStyling() {
            // Add hover effects to table rows
            $('#lwTemplatesList tbody tr').hover(
                function() {
                    $(this).addClass('table-row-hover');
                },
                function() {
                    $(this).removeClass('table-row-hover');
                }
            );

            // Add click effects to table headers
            $('#lwTemplatesList thead th').click(function() {
                $('#lwTemplatesList thead th').removeClass('sort-active');
                $(this).addClass('sort-active');
            });
        }

        // Add modern table row hover class
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .table-row-hover {
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
                    transform: translateY(-1px) !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                }
                .sort-active {
                    background: linear-gradient(135deg, #007bff, #0056b3) !important;
                    color: white !important;
                }
                .sort-active .sort-icon {
                    color: white !important;
                }
            `)
            .appendTo('head');

        // Start initialization after a short delay to ensure DataTable is ready
        setTimeout(initializeCustomFeatures, 500);
    });
</script>

<style>
    /* Modern Table Container Styles */
    .modern-table-container {
        background: #ffffff !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
        overflow: hidden !important;
        margin-bottom: 2rem !important;
        border: 1px solid #e9ecef !important;
    }

    /* Table Header Controls */
    .table-header-controls {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1.5rem 2rem !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .table-title-section {
        display: flex !important;
        align-items: center !important;
    }

    .table-title {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        color: #495057 !important;
        margin: 0 !important;
    }

    .table-controls {
        display: flex !important;
        align-items: center !important;
        gap: 1.5rem !important;
    }

    .entries-control {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    .entries-control label {
        font-size: 0.875rem !important;
        color: #6c757d !important;
        margin: 0 !important;
    }

    .entries-select {
        padding: 0.375rem 0.75rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 6px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 60px !important;
    }

    .entries-text {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    .search-control {
        position: relative !important;
    }

    .search-input {
        padding: 0.5rem 1rem 0.5rem 2.5rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 8px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 200px !important;
        transition: all 0.3s ease !important;
    }

    .search-input:focus {
        outline: none !important;
        border-color: #007bff !important;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
    }

    .search-icon {
        position: absolute !important;
        left: 0.75rem !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #6c757d !important;
        font-size: 0.875rem !important;
    }

    /* Table Header Controls */
    .table-header-controls {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 1.5rem 2rem !important;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
        border-bottom: 1px solid #dee2e6 !important;
    }

    .table-title-section {
        display: flex !important;
        align-items: center !important;
    }

    .table-title {
        font-size: 1.25rem !important;
        font-weight: 600 !important;
        color: #495057 !important;
        margin: 0 !important;
    }

    .table-controls {
        display: flex !important;
        align-items: center !important;
        gap: 1.5rem !important;
    }

    .entries-control {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
    }

    .entries-control label {
        font-size: 0.875rem !important;
        color: #6c757d !important;
        margin: 0 !important;
    }

    .entries-select {
        padding: 0.375rem 0.75rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 6px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 60px !important;
    }

    .entries-text {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    .search-control {
        position: relative !important;
    }

    .search-input {
        padding: 0.5rem 1rem 0.5rem 2.5rem !important;
        border: 1px solid #ced4da !important;
        border-radius: 8px !important;
        background: #ffffff !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        min-width: 200px !important;
        transition: all 0.3s ease !important;
    }

    .search-input:focus {
        outline: none !important;
        border-color: #007bff !important;
        box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1) !important;
    }

    .search-icon {
        position: absolute !important;
        left: 0.75rem !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        color: #6c757d !important;
        font-size: 0.875rem !important;
    }

    /* Modern DataTable Styles */
    .modern-datatable {
        width: 100% !important;
        border-collapse: collapse !important;
    }

    .modern-datatable thead th {
        background: #f8f9fa !important;
        border: none !important;
        padding: 1rem 1.5rem !important;
        font-weight: 600 !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
        position: relative !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable thead th:hover {
        background: #e9ecef !important;
    }

    

    .modern-datatable thead th:hover::after {
        color: #007bff !important;
    }

    .modern-datatable tbody tr {
        border-bottom: 1px solid #f1f3f4 !important;
        transition: all 0.3s ease !important;
    }

    .modern-datatable tbody tr:hover {
        background: #f8f9fa !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    }

    .modern-datatable tbody td {
        padding: 1rem 1.5rem !important;
        font-size: 0.875rem !important;
        color: #495057 !important;
        vertical-align: middle !important;
    }

    /* Status Badge Styles */
    .status-badge {
        display: inline-flex !important;
        align-items: center !important;
        padding: 0.5rem 1rem !important;
        border-radius: 50px !important;
        font-weight: 600 !important;
        font-size: 0.75rem !important;
        letter-spacing: 0.5px !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08) !important;
        transition: all 0.3s ease !important;
        text-transform: uppercase !important;
    }

    .status-badge:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12) !important;
    }

    .status-icon {
        margin-right: 0.5rem !important;
        font-size: 0.875rem !important;
    }

    .status-approved {
        background: linear-gradient(135deg, #d4edda, #c3e6cb) !important;
        color: #155724 !important;
        border-left: 3px solid #28a745 !important;
    }

    .status-rejected {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb) !important;
        color: #721c24 !important;
        border-left: 3px solid #dc3545 !important;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7) !important;
        color: #856404 !important;
        border-left: 3px solid #ffc107 !important;
    }

    .status-other {
        background: linear-gradient(135deg, #e9ecef, #dee2e6) !important;
        color: #495057 !important;
        border-left: 3px solid #adb5bd !important;
    }

    /* Action Buttons Container */
    .action-buttons-container {
        display: flex !important;
        align-items: center !important;
        gap: 0.5rem !important;
        justify-content: flex-start !important;
    }

    .action-btn {
        width: 36px !important;
        height: 36px !important;
        border: none !important;
        border-radius: 8px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 0.875rem !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        position: relative !important;
        overflow: hidden !important;
        text-decoration: none !important;
    }

    .action-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        text-decoration: none !important;
    }

    .action-btn-hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
    }

    .campaign-btn {
        background: linear-gradient(135deg, #28a745, #20c997) !important;
        color: white !important;
    }

    .campaign-btn:hover {
        background: linear-gradient(135deg, #218838, #1ea085) !important;
        color: white !important;
    }

    .preview-btn {
        background: linear-gradient(135deg, #007bff, #0056b3) !important;
        color: white !important;
    }

    .preview-btn:hover {
        background: linear-gradient(135deg, #0056b3, #004085) !important;
        color: white !important;
    }

    .edit-btn {
        background: linear-gradient(135deg, #ffc107, #e0a800) !important;
        color: white !important;
    }

    .edit-btn:hover {
        background: linear-gradient(135deg, #e0a800, #d39e00) !important;
        color: white !important;
    }

    .delete-btn {
        background: linear-gradient(135deg, #dc3545, #c82333) !important;
        color: white !important;
    }

    .delete-btn:hover {
        background: linear-gradient(135deg, #c82333, #bd2130) !important;
        color: white !important;
    }

    /* Table Footer */
    .table-footer {
        padding: 1rem 2rem !important;
        background: #f8f9fa !important;
        border-top: 1px solid #dee2e6 !important;
    }

    .table-info {
        font-size: 0.875rem !important;
        color: #6c757d !important;
    }

    /* DataTables Custom Styling */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        display: none !important;
    }

    /* Hide default DataTables elements since we have custom ones */
    .dataTables_wrapper .top {
        display: none !important;
    }

    .dataTables_wrapper .bottom {
        display: none !important;
    }

    /* Modern Button Styles */
    .btn-modern {
        transition: all 0.3s ease !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 6px rgba(50, 50, 93, 0.11), 0 1px 3px rgba(0, 0, 0, 0.08) !important;
        font-weight: 600 !important;
        letter-spacing: 0.025em !important;
        padding: 0.5rem 1rem !important;
        margin: 0 0.2rem !important;
    }

    .btn-modern:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 7px 14px rgba(50, 50, 93, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08) !important;
    }

    .btn-modern-primary {
        background: linear-gradient(135deg, #6e8efb, #4a6cf7) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-secondary {
        background: linear-gradient(135deg, #6edffb, #4aa3f7) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-green {
        background: linear-gradient(135deg, #0B7753, #22A755) !important;
        border: none !important;
        color: white !important;
    }

    .btn-modern-green:hover {
        background: linear-gradient(135deg, #22A755, #1D9248) !important;
        color: white !important;
    }

    .btn-rounded {
        border-radius: 12px !important;
    }

    /* Pulse Animation */
    .pulse {
        animation: pulse-animation 2s infinite !important;
    }

    @keyframes pulse-animation {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* Page Title Styling */
    .page-title {
        font-size: 1.75rem !important;
        font-weight: 600 !important;
        color: #333 !important;
        margin: 0 !important;
        padding: 0 !important;
        line-height: 1.2 !important;
        animation: fadeInLeft 0.5s ease-out !important;
    }

    @keyframes fadeInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* Responsive Design */
    @media (max-width: 1200px) {
        .table-header-controls {
            flex-direction: column !important;
            gap: 1rem !important;
            align-items: stretch !important;
        }

        .table-controls {
            justify-content: space-between !important;
        }
    }

    @media (max-width: 768px) {
        .table-header-controls {
            padding: 1rem !important;
        }

        .table-controls {
            flex-direction: column !important;
            gap: 1rem !important;
        }

        .entries-control {
            justify-content: center !important;
        }

        .search-control {
            width: 100% !important;
        }

        .search-input {
            width: 100% !important;
            min-width: auto !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td {
            padding: 0.75rem 1rem !important;
        }

        .action-buttons-container {
            flex-wrap: wrap !important;
            gap: 0.25rem !important;
        }

        .action-btn {
            width: 32px !important;
            height: 32px !important;
            font-size: 0.75rem !important;
        }

        .page-title {
            font-size: 1.5rem !important;
        }

        .col-xl-12 .d-flex.justify-content-between {
            flex-wrap: wrap !important;
            gap: 15px !important;
        }

        .col-xl-12 .d-flex.justify-content-between > div {
            width: 100% !important;
            justify-content: center !important;
        }
    }

    @media (max-width: 576px) {
        .table-title {
            font-size: 1.125rem !important;
        }

        .modern-datatable thead th,
        .modern-datatable tbody td {
            padding: 0.5rem 0.75rem !important;
            font-size: 0.8rem !important;
        }

        .status-badge {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.7rem !important;
        }

        .action-btn {
            width: 28px !important;
            height: 28px !important;
            font-size: 0.7rem !important;
        }

        .btn-modern {
            padding: 0.375rem 0.75rem !important;
            font-size: 0.875rem !important;
        }
    }

    /* DataTables Custom Styling */
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        display: none !important;
    }

    /* Hide default DataTables elements since we have custom ones */
    .dataTables_wrapper .top {
        display: none !important;
    }

    .dataTables_wrapper .bottom {
        display: none !important;
    }
</style>
@endsection()
