

<?php $__env->startSection('content'); ?>
<?php echo $__env->make('users.partials.header', [
'title' => (hasSystemAdminAccess() ? __tr('Super Admins') : __tr('Users')) . ' '. auth()->user()->name,
'description' => '',
'class' => 'col-lg-7'
], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<style>
    th{
        background-color:rgb(11, 119, 83) !important;
    }
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-xl-12 mb-3 mt-md--5">
            <button type="button" class="btn btn-primary mt-5" data-toggle="modal" data-target="#addVendorModal">
                <?php if(hasSystemAdminAccess()): ?>
                    <?= __tr('Add New Super Admin') ?>
                <?php else: ?>
                    <?= __tr('Add New User') ?>
                <?php endif; ?>
            </button>
        </div>
    <div class="d-flex justify-content-between align-items-center flex-wrap mt-5">
        <!-- Left: Title -->
        <h1 class="page-title mb-0" style="color: #22A755;">
            <i class="fa fa-users"></i> <?php echo e(__tr(' Users')); ?>

        </h1>

        <!-- Right: Button -->
        <button type="button" class="btn mt-3 mt-md-0 text-white"
                data-toggle="modal"
                data-target="#addVendorModal"
                style="background-color: #1e7c4a; border-color: #1e7c4a; transition: all 0.3s ease;">
            <i class="fas fa-user-plus me-2"></i><?php echo e(__tr(' Add New User')); ?>

        </button>

        <!-- Optional Hover Animation -->
        <style>
            button[data-target="#addVendorModal"]:hover {
                background-color: #18643c;
                border-color: #18643c;
                transform: scale(1.02);
            }
        </style>

    </div>
</div>

        <div class="col-xl-12">
           <?php if (isset($component)) { $__componentOriginal49c2f9c26fb91807a4f87ab8f845e982 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.datatable','data' => ['id' => 'lwManageVendorsTable','url' => route('central.vendors.read.list'),'dataPageLength' => '100']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.datatable'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwManageVendorsTable','url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('central.vendors.read.list')),'data-page-length' => '100']); ?>
                <th data-template="#titleExtendedButtons" data-orderable="true" data-name="title">
                    <?= hasSystemAdminAccess() ? __tr('Super Admin') : __tr('User') ?>
                </th>
                <th data-template="#lwQuickActionButtons" data-orderable="true" data-name="title">
                    <?= __tr('Quick Actions') ?>
                </th>
                <th data-orderable="true" data-name="fullName">
                    <?= hasSystemAdminAccess() ? __tr('Name') : __tr('Admin Name') ?>
                </th>
                
                <th data-orderable="true" data-name="email">
                    <?= __tr('email') ?>
                </th>
                <th data-orderable="true" data-name="status">
                    <?= __tr('status') ?>
                </th>
                <th data-orderable="true" data-name="mobile_number">
                    <?= __tr('Mobile Number') ?>
                </th>
                <th data-orderable="true" data-name="user_status">
                    <?= hasSystemAdminAccess() ? __tr('Super Admin Status') : __tr('Admin Status') ?>
                </th>
                <th data-orderable="true" data-name="created_at">
                    <?= __tr('Created On') ?>
                </th>
                <th data-template="#actionButtons" name="null">
                    <?= __tr('Action') ?>
                </th>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982)): ?>
<?php $attributes = $__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982; ?>
<?php unset($__attributesOriginal49c2f9c26fb91807a4f87ab8f845e982); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49c2f9c26fb91807a4f87ab8f845e982)): ?>
<?php $component = $__componentOriginal49c2f9c26fb91807a4f87ab8f845e982; ?>
<?php unset($__componentOriginal49c2f9c26fb91807a4f87ab8f845e982); ?>
<?php endif; ?>
        </div>
    </div>
    <script type="text/template" id="titleExtendedButtons">
        <a  href ="<%= __Utils.apiURL("<?php echo e(route('vendor.dashboard',['vendorIdOrUid'=>'vendorIdOrUid'])); ?>", {'vendorIdOrUid':__tData._uid}) %>"> <%-__tData.title %> </a> 
    </script>
    <script type="text/template" id="lwQuickActionButtons">
        <?php if(hasSystemAdminAccess()): ?>
        <a data-method="post" href="<%= __Utils.apiURL("<?php echo e(route('central.vendors.user.write.login_as', [ 'vendorUid'])); ?>", {'vendorUid': __tData._uid}) %>" class="btn btn-outline-success btn-sm lw-ajax-link-action" title="<?php echo e(__tr('Login as Super Admin')); ?>"><i class="fa fa-sign-in-alt"></i> <?php echo e(__tr('Login')); ?></a>
        <?php else: ?>
        <a data-method="post" href="<%= __Utils.apiURL("<?php echo e(route('central.vendors.user.write.login_as', [ 'vendorUid'])); ?>", {'vendorUid': __tData._uid}) %>" class="btn btn-outline-success btn-sm lw-ajax-link-action"  title="<?php echo e(__tr('Login as Vendor Admin')); ?>"><i class="fa fa-sign-in-alt"></i> <?php echo e(__tr('Login')); ?></a>
        <a class="btn btn-info btn-sm" href ="<%= __Utils.apiURL("<?php echo e(route('central.vendor.details',['vendorIdOrUid'=>'vendorIdOrUid'])); ?>", {'vendorIdOrUid':__tData._uid}) %>"> <?php echo e(__tr('Subscription')); ?> </a>
        <?php endif; ?>
    </script>
    <script type="text/template" id="actionButtons">
        <!-- EDIT ACTION -->
        <?php if(hasSystemAdminAccess()): ?>
        <a data-pre-callback="appFuncs.clearContainer" title="<?php echo e(__tr('Edit')); ?>" class="lw-btn btn btn-sm btn-default lw-ajax-link-action" data-response-template="#lwEditVendorBody" href="<%= __Utils.apiURL("<?php echo e(route('system.admin.super_admins.read.update.data', ['superAdminUid'])); ?>", {'superAdminUid': __tData._uid}) %>" data-toggle="modal" data-target="#lwEditVendor"><i class="fa fa-edit"></i> <?php echo e(__tr('Edit')); ?></a>
        <?php else: ?>
        <a data-pre-callback="appFuncs.clearContainer" title="<?php echo e(__tr('Edit')); ?>" class="lw-btn btn btn-sm btn-default lw-ajax-link-action" data-response-template="#lwEditVendorBody" href="<%= __Utils.apiURL("<?php echo e(route('vendor.read.update.data', ['vendorIdOrUid'])); ?>", {'vendorIdOrUid': __tData._uid}) %>" data-toggle="modal" data-target="#lwEditVendor"><i class="fa fa-edit"></i> <?php echo e(__tr('Edit')); ?></a>
        <?php endif; ?>
        <% if(__tData.status_code != 5 ) { %>
        <!--  DELETE ACTION -->
        <a data-method="post" href="<%= __Utils.apiURL("<?php echo e(route('vendor.delete', ['vendorIdOrUid'])); ?>", {'vendorIdOrUid': __tData._uid}) %>" class="btn btn-outline-warning btn-sm lw-ajax-link-action-via-confirm" data-confirm="#lwSoftDeleteVendor-template" title="<?php echo e(__tr('Soft Delete')); ?>" data-toggle="modal" data-target="#deletePlan" data-callback-params="<?php echo e(json_encode(['modalId' => '#lwSoftDeleteVendor-template','datatableId' => '#lwManageVendorsTable'])); ?>" data-callback="appFuncs.modelSuccessCallback"><i class="fa fa-trash"></i> <?php echo e(__tr('Soft Delete')); ?></a>
        <!--  PASSWORD ACTION -->
        <% } %>
        <a data-pre-callback="appFuncs.clearContainer" title="<?php echo e(__tr('Change Password')); ?>" class="lw-btn btn btn-sm btn-dark lw-ajax-link-action" data-response-template="#lwChangePasswordBody" href="<%= __Utils.apiURL(" <?php echo e(route('vendor.change.password.data',['vendorIdOrUid'])); ?>", {'vendorIdOrUid': __tData.userId}) %>" data-toggle="modal" data-target="#lwChangePasswordAuthor"><i class="fas fa-key"></i> <?php echo e(__tr('Change Password')); ?></a>
           <!-- PERMANANT DELTE ACTION -->
        <a data-method="post" href="<%= __Utils.apiURL("<?php echo e(route('vendor.permanant.delete', ['vendorIdOrUid'])); ?>", {'vendorIdOrUid': __tData._uid}) %>" class="btn btn-outline-danger btn-sm lw-ajax-link-action-via-confirm" data-confirm="#lwDeleteVendor-template" title="<?php echo e(__tr('Delete')); ?>" data-toggle="modal" data-target="#deletePlan" data-callback-params="<?php echo e(json_encode(['modalId' => '#lwDeleteVendor-template','datatableId' => '#lwManageVendorsTable'])); ?>" data-callback="appFuncs.modelSuccessCallback"><i class="fa fa-trash"></i> <?php echo e(__tr('Delete')); ?></a>
           <!--  /PERMANANT DELTE ACTION -->
    </script>
    
    <!-- VENDOR DELETE TEMPLATE -->
    <script type="text/template" id="lwSoftDeleteVendor-template">
        <h2><?= __tr('Are You Sure!') ?></h2>
            <p><?= hasSystemAdminAccess() ? __tr('You want to Soft delete this Super Admin?') : __tr('You want to Soft delete this Vendor?') ?></p>
    </script>
    <!-- /VENDOR DELETE TEMPLATE -->
     <!-- VENDOR PERMANENT DELETE TEMPLATE -->
     <script type="text/template" id="lwDeleteVendor-template">
        <h2><?= __tr('Are You Sure!') ?></h2>
            <p><?= hasSystemAdminAccess() ? __tr('You want to delete this Super Admin permanently?') : __tr('You want to delete this Vendor, It will delete vendor and its data permanently ?') ?></p>
    </script>
    <!-- /VENDOR PERMANENT DELETE TEMPLATE -->
    
    <?php if (isset($component)) { $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.modal','data' => ['id' => 'addVendorModal','header' => hasSystemAdminAccess() ? __tr('Add New Super Admin') : __tr('Add New User'),'hasForm' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'addVendorModal','header' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(hasSystemAdminAccess() ? __tr('Add New Super Admin') : __tr('Add New User')),'hasForm' => true]); ?>
        
        <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['action' => route('central.vendors.write.add'),'dataCallback' => 'afterSuccessfullyCreated']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('central.vendors.write.add')),'data-callback' => 'afterSuccessfullyCreated']); ?>
            <div class="lw-form-modal-body">
                
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('User Title')); ?>" type="text"
                            name="vendor_title" value="<?php echo e(old('vendor_title')); ?>" required>
                    </div>
                </div>
                
                <?php if(hasSystemAdminAccess()): ?>
                
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-building"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('Company Name')); ?>" type="text"
                            name="company_name" value="<?php echo e(old('company_name')); ?>" required>
                    </div>
                </div>
                
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-info-circle"></i></span>
                        </div>
                        <textarea class="form-control" placeholder="<?php echo e(__tr('Company Description')); ?>" 
                            name="company_description" rows="3"><?php echo e(old('company_description')); ?></textarea>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="text-center text-muted mb-4 ">
                    <?php if(hasSystemAdminAccess()): ?>
                        <?php echo e(__tr('Super Admin User')); ?>

                    <?php else: ?>
                        <?php echo e(__tr('Admin User')); ?>

                    <?php endif; ?>
                </div>
                
                
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('First Name')); ?>" type="text" name="first_name"
                            value="<?php echo e(old('first_name')); ?>" required>
                    </div>
                </div>
                
                
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('Last Name')); ?>" type="text" name="last_name"
                            value="<?php echo e(old('last_name')); ?>" required>
                    </div>
                </div>
                
                
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fas fa-mobile-alt"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('Mobile Number')); ?>" type="number" name="mobile_number" required >
                    </div>
                    <h5><span class="text-muted"><?php echo e(__tr("Mobile number should be with country code without 0 or +")); ?></span></h5>
                </div>
               
                
                
                <div class="form-group mb-3">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-at"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('Email')); ?>" type="email" name="email" required >
                    </div>
                </div>
                
                
                <div class="form-group">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input class="form-control" name="password" placeholder="<?php echo e(__tr('Password')); ?>" type="password"
                            required>
                    </div>
                </div>
                
                
                <div class="form-group">
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('Confirm Password')); ?>" type="password"
                            name="password_confirmation" required>
                    </div>
                </div>
                

                <?php if(hasCentralAccess()): ?>
                
                <div class="form-group mt-4">
                    <label class="form-control-label"><?php echo e(__tr('Module Permissions')); ?></label>
                    <div class="card border">
                        <div class="card-body p-3">
                            <div class="row">
                                <?php if(hasSystemAdminAccess()): ?>
                                    
                                    <?php $__currentLoopData = \App\Yantrana\Components\Auth\Models\AuthModel::$availableModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moduleKey => $moduleTitle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="<?php echo e($moduleKey); ?>" id="module<?php echo e(ucfirst($moduleKey)); ?>">
                                                <label class="custom-control-label" for="module<?php echo e(ucfirst($moduleKey)); ?>">
                                                    <?php echo e($moduleTitle); ?>

                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    
                                    <?php $__currentLoopData = getAssignableModules(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moduleKey => $moduleTitle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="<?php echo e($moduleKey); ?>" id="module<?php echo e(ucfirst($moduleKey)); ?>">
                                                <label class="custom-control-label" for="module<?php echo e(ucfirst($moduleKey)); ?>">
                                                    <?php echo e($moduleTitle); ?>

                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                            <small class="text-muted">
                                <?php if(hasSystemAdminAccess()): ?>
                                    <?php echo e(__tr('Select the modules that this Super Admin will have access to. Their vendors will only see the selected modules.')); ?>

                                <?php else: ?>
                                    <?php echo e(__tr('Select the modules that this user will have access to. You can only assign modules that you have access to.')); ?>

                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>
                
                <?php endif; ?>
            </div>
            
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary"><?php echo e(__tr('Add')); ?></button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <?= __tr('Close') ?>
                </button>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $attributes = $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $component = $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
    <!-- EDIT VENDOR/SUPER ADMIN MODAL -->
    <?php if (isset($component)) { $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.modal','data' => ['id' => 'lwEditVendor','header' => hasSystemAdminAccess() ? __tr('Edit Super Admin') : __tr('Edit User'),'hasForm' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwEditVendor','header' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(hasSystemAdminAccess() ? __tr('Edit Super Admin') : __tr('Edit User')),'hasForm' => true]); ?>
        <!-- EDIT VENDOR FORM  -->
        <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['id' => 'lwEditPlanForm','action' => hasSystemAdminAccess() ? route('system.admin.super_admins.write.update') : route('vendor.write.update'),'dataCallbackParams' => ['modalId' => '#lwEditVendor', 'datatableId' => '#lwManageVendorsTable'],'dataCallback' => 'appFuncs.modelSuccessCallback']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwEditPlanForm','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(hasSystemAdminAccess() ? route('system.admin.super_admins.write.update') : route('vendor.write.update')),'data-callback-params' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['modalId' => '#lwEditVendor', 'datatableId' => '#lwManageVendorsTable']),'data-callback' => 'appFuncs.modelSuccessCallback']); ?>
            <!-- form body -->
            <div data-default-text="<?php echo e(__tr('Please wait while we fetch data')); ?>" id="lwEditVendorBody"
                class="lw-form-modal-body"></div>
            <script type="text/template" id="lwEditVendorBody-template">
                <input type="hidden" name="vendorIdOrUid" value="<%- __tData._uid %>" />
                    <input type="hidden" name="userIdOrUid" value="<%- __tData.userUId %>" />
                <!-- FORM FIELDS -->
                <!-- TITLE -->
                <div class="form-group">
                    <label for="lwTitleField"><?= __tr('Title') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('Title') ?>" id="lwTitleField" value="<%- __tData.title %>" name="title"/>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwTitleField" placeholder="<?= __tr('Title') ?>"
                        value="<%- __tData.title %>" name="title" />
                </div>
                <!-- /Title -->
                
                <div class="text-center text-muted mt-4 mb-0 ">
                    <?php if(hasSystemAdminAccess()): ?>
                        <?php echo e(__tr('Super Admin User')); ?>

                    <?php else: ?>
                        <?php echo e(__tr('Admin User')); ?>

                    <?php endif; ?>
                </div>
                <!-- UserName  -->
                <div class="form-group">
                    <label for="lwUserNameEditField"><?= __tr('Username') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-id-card"></i></span>
                        </div>
                        <input type="text"class="lw-form-field form-control" placeholder="<?= __tr('Email ') ?>" id="lwEmailEditField" value="<%- __tData.email%>" name="email" />
                    </div>
                </div>
                <!-- /UserName  -->
                <!-- FIRST NAME -->
                <div class="form-group">
                    <label for="lwDescriptionField"><?= __tr('First Name') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('First Name') ?>" id="lwFirstNameField" value="<%- __tData.first_name %>" name="first_name"/>
                    </div>
                </div>
                <!-- /FIRST NAME -->
                <!-- LAST NAME -->
                <div class="form-group">
                    <label for="lwDescriptionField"><?= __tr('Last Name') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-user-alt"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('Last Name') ?>" id="lwLastNameField" value="<%- __tData.last_name %>" name="last_name"/>
                    </div>
                </div>
                <!-- /LAST NAME -->
                 <!-- MOBILE NUMBER -->
                <div class="form-group mb-3">
                    <label for="lwMobileNumberField"><?= __tr('Mobile Number') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fas fa-mobile-alt"></i></span>
                        </div>
                        <input class="form-control" placeholder="<?php echo e(__tr('Mobile Number')); ?>" value="<%- __tData.mobile_number %>" type="number" name="mobile_number" required >
                    </div>
                    <h5><span class="text-muted "><?php echo e(__tr("Mobile number should be with country code without 0 or +")); ?></span></h5>
                </div>
                 <!-- /MOBILE NUMBER -->
                <!-- EMAIL  -->
                <div class="form-group">
                    <label for="lwEmailEditField"><?= __tr('Email') ?></label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-at"></i></span>
                        </div>
                        <input type="text" class="lw-form-field form-control" placeholder="<?= __tr('Email ') ?>" id="lwEmailEditField" value="<%- __tData.email%>" name="email" required="true" />
                    </div>
                </div>
                <!-- /EMAIL  -->
                <!-- STATUS -->
                <div class="form-group pt-3">
                    <?php if(!hasSystemAdminAccess()): ?>
                    <label for="lwIsVendorActiveEditField"><?php echo e(__tr('Vendor Status')); ?></label>
                    <input type="checkbox" id="lwIsVendorActiveEditField" <%- __tData.store_status == 1 ? 'checked' : '' %> data-lw-plugin="lwSwitchery" name="store_status">
                    <?php endif; ?>
                </div>
                <!-- /STATUS -->
                <!-- STATUS -->
                <div class="form-group pt-3">
                    <label for="lwIsActiveEditField"><?php echo e(hasSystemAdminAccess() ? __tr('Super Admin Status') : __tr('Admin User Status')); ?></label>
                    <input type="hidden" name="status" value="0" />
                    <input type="checkbox" id="lwIsActiveEditField" value="1" <%- __tData.status == 1 ? 'checked' : '' %> data-lw-plugin="lwSwitchery" name="status">
                </div>
                <!-- /STATUS -->

                <?php if(hasCentralAccess()): ?>
                <!-- MODULE PERMISSIONS -->
                <div class="form-group mt-4">
                    <label class="form-control-label"><?php echo e(__tr('Module Permissions')); ?></label>
                    <div class="card border">
                        <div class="card-body p-3">
                            <div class="row">
                                <?php if(hasSystemAdminAccess()): ?>
                                    
                                    <?php $__currentLoopData = \App\Yantrana\Components\Auth\Models\AuthModel::$availableModules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moduleKey => $moduleTitle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="<?php echo e($moduleKey); ?>" id="module<?php echo e(ucfirst($moduleKey)); ?>Edit"
                                                    <%- __tData.module_permissions && __tData.module_permissions.includes('<?php echo e($moduleKey); ?>') ? 'checked' : '' %>>
                                                <label class="custom-control-label" for="module<?php echo e(ucfirst($moduleKey)); ?>Edit">
                                                    <?php echo e($moduleTitle); ?>

                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                    
                                    <?php $__currentLoopData = getAssignableModules(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $moduleKey => $moduleTitle): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6 mb-2">
                                            <div class="custom-control custom-checkbox">
                                                <input type="checkbox" class="custom-control-input" name="module_permissions[]" 
                                                    value="<?php echo e($moduleKey); ?>" id="module<?php echo e(ucfirst($moduleKey)); ?>Edit"
                                                    <%- __tData.module_permissions && __tData.module_permissions.includes('<?php echo e($moduleKey); ?>') ? 'checked' : '' %>>
                                                <label class="custom-control-label" for="module<?php echo e(ucfirst($moduleKey)); ?>Edit">
                                                    <?php echo e($moduleTitle); ?>

                                                </label>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </div>
                            <small class="text-muted">
                                <?php if(hasSystemAdminAccess()): ?>
                                    <?php echo e(__tr('Select the modules that this Super Admin will have access to. Their vendors will only see the selected modules.')); ?>

                                <?php else: ?>
                                    <?php echo e(__tr('Select the modules that this user will have access to. You can only assign modules that you have access to.')); ?>

                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                </div>
                <!-- /MODULE PERMISSIONS -->
                <?php endif; ?>
            </script>
            <!-- FORM FOOTER -->
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary"><?php echo e(__tr('Submit')); ?></button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Close')); ?></button>
            </div>

            <!-- Section Title -->
            <div class="text-center text-success mt-4 mb-3 font-weight-bold">
                <?php echo e(__tr('Admin User')); ?>

            </div>

            <!-- Username / Email -->
            <div class="form-group mb-3">
                <label for="lwUserNameEditField" class="font-weight-bold text-dark"><?= __tr('Username') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-id-card me-2"></i></span>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwUserNameEditField" placeholder="<?= __tr('Username') ?>"
                        value="<%- __tData.username %>" name="username" />
                </div>
            </div>

            <!-- First Name -->
            <div class="form-group mb-3">
                <label for="lwFirstNameField" class="font-weight-bold text-dark"><?= __tr('First Name') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-user-alt me-2"></i></span>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwFirstNameField" placeholder="<?= __tr('First Name') ?>"
                        value="<%- __tData.first_name %>" name="first_name" />
                </div>
            </div>

            <!-- Last Name -->
            <div class="form-group mb-3">
                <label for="lwLastNameField" class="font-weight-bold text-dark"><?= __tr('Last Name') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-user-alt me-2"></i></span>
                    </div>
                    <input type="text" class="form-control border-success shadow-green"
                        id="lwLastNameField" placeholder="<?= __tr('Last Name') ?>"
                        value="<%- __tData.last_name %>" name="last_name" />
                </div>
            </div>

            <!-- Mobile Number -->
            <div class="form-group mb-3">
                <label for="lwMobileNumberField" class="font-weight-bold text-dark"><?= __tr('Mobile Number') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fas fa-mobile-alt me-2"></i></span>
                    </div>
                    <input type="number" class="form-control border-success shadow-green"
                        name="mobile_number" value="<%- __tData.mobile_number %>"
                        placeholder="<?php echo e(__tr('Mobile Number')); ?>" required />
                </div>
                <small class="text-muted d-block mt-1"><?php echo e(__tr('Mobile number should be with country code without 0 or +')); ?></small>
            </div>

            <!-- Email -->
            <div class="form-group mb-3">
                <label for="lwEmailEditField" class="font-weight-bold text-dark"><?= __tr('Email') ?></label>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text bg-success text-white px-3"><i class="fa fa-at me-2"></i></span>
                    </div>
                    <input type="email" class="form-control border-success shadow-green"
                        id="lwEmailEditField" placeholder="<?= __tr('Email') ?>"
                        value="<%- __tData.email %>" name="email" required />
                </div>
            </div>

            <!-- Vendor Status -->
            <div class="form-group pt-3">
                <label for="lwIsVendorActiveEditField" class="font-weight-bold text-dark">
                    <?php echo e(__tr('Vendor Status')); ?>

                </label>
                <input type="checkbox" id="lwIsVendorActiveEditField"
                    <%- __tData.store_status == 1 ? 'checked' : '' %>
                    data-lw-plugin="lwSwitchery" name="store_status">
            </div>

            <!-- Admin User Status -->
            <div class="form-group pt-3">
                <label for="lwIsActiveEditField" class="font-weight-bold text-dark">
                    <?php echo e(__tr('Admin User Status')); ?>

                </label>
                <input type="checkbox" id="lwIsActiveEditField"
                    <%- __tData.status == 1 ? 'checked' : '' %>
                    data-lw-plugin="lwSwitchery" name="status">
            </div>
        </script>

        <!-- FORM FOOTER -->
        <div class="modal-footer">
            <button type="submit" class="btn btn-success font-weight-bold"><?php echo e(__tr('Submit ')); ?> <i class="fa fa-paper-plane"></i></button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Close')); ?></button>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $attributes = $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $component = $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>

    <!-- EDIT VENDOR MODAL END -->
    <!-- FOR CHANGE PASSWORD FOR VENDOR -->
    <?php if (isset($component)) { $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.modal','data' => ['id' => 'lwChangePasswordAuthor','header' => __tr('Change Password'),'hasForm' => true]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['id' => 'lwChangePasswordAuthor','header' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(__tr('Change Password')),'hasForm' => true]); ?>
        <!-- EDIT ACCOUNT FORM -->
        <?php if (isset($component)) { $__componentOriginald0b55ee435ec3aeeadffee8b0df479da = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.lw.form','data' => ['class' => 'mb-0','id' => 'lwChangeAuthorPassword','action' => route('auth.vendor.change.password'),'dataCallbackParams' => ['modalId' => '#lwChangePasswordAuthor','datatableId' => '#lwAccountList'],'dataCallback' => 'appFuncs.modelSuccessCallback','dataSecured' => 'true']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lw.form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'mb-0','id' => 'lwChangeAuthorPassword','action' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('auth.vendor.change.password')),'data-callback-params' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(['modalId' => '#lwChangePasswordAuthor','datatableId' => '#lwAccountList']),'data-callback' => 'appFuncs.modelSuccessCallback','data-secured' => 'true']); ?>
            <!-- FORM BODY -->
            <div id="lwChangePasswordBody" class="lw-form-modal-body"></div>
            <script type="text/template" id="lwChangePasswordBody-template">
                <!-- FORM FIELDS -->
                <input type="hidden" name="users_id" value="<%-__tData._id %>" />
                <!-- for new password -->
                <div class="form-group">
                    <label for="lwNewPasswordField">
                        <?= __tr('New Password') ?>
                    </label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input type="password" class="lw-form-field form-control <?php echo e($errors->has('password') ? ' is-invalid' : ''); ?>" placeholder="<?= __tr('New Password') ?>" id="lwNewPasswordField" value="" name="password" />
                    </div>
                </div>
                <!-- /NEW PASSWORD -->
                <!-- CONFIRM NEW PASSWORD -->
                <div class="form-group">
                    <label for="lwConfirmNewPasswordField">
                        <?= __tr('Confirm New Password') ?>
                    </label>
                    <div class="input-group input-group-alternative">
                        <div class="input-group-prepend">
                            <span class="input-group-text"><i class="fa fa-key"></i></span>
                        </div>
                        <input type="password" class="lw-form-field form-control"
                        placeholder="<?= __tr('Confirm New Password') ?>" id="lwConfirmNewPasswordField" value=""
                        name="password_confirmation" />
                    </div>
                </div>
                <!-- /CONFIRM NEW PASSWORD-->
            </script>
            <!-- FORM FOOTER -->
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary"><?php echo e(__tr('Change Password')); ?></button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__tr('Close')); ?></button>
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $attributes = $__attributesOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__attributesOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da)): ?>
<?php $component = $__componentOriginald0b55ee435ec3aeeadffee8b0df479da; ?>
<?php unset($__componentOriginald0b55ee435ec3aeeadffee8b0df479da); ?>
<?php endif; ?>
        <!--/  EDIT VENDOR FORM -->
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $attributes = $__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__attributesOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f)): ?>
<?php $component = $__componentOriginal64816a37b1766c5cb5d0bcd192fb685f; ?>
<?php unset($__componentOriginal64816a37b1766c5cb5d0bcd192fb685f); ?>
<?php endif; ?>

    <!--/ EDIT VENDOR MODAL -->
    <?php $__env->startPush('footer'); ?>
    <?php $__env->stopPush(); ?>
    <?php $__env->startPush('appScripts'); ?>
    <script>
        (function($) {
            'use strict';
            window.afterSuccessfullyCreated = function (responseData) {
            if (responseData.reaction == 1) {
                __Utils.viewReload();
            }
        }
        })(jQuery);
    </script>
    <?php $__env->stopPush(); ?>

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', ['title' => hasSystemAdminAccess() ? __tr('Super Admins') : __tr('Users')], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\omx-flow-new\resources\views/vendors/list.blade.php ENDPATH**/ ?>